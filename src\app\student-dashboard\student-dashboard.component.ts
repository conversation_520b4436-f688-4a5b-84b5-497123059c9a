import { Component, OnInit, OnD<PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription, interval } from 'rxjs';
import { BaseDashboardComponent } from '../shared/base-dashboard.component';
import { AuthService } from '../services/auth.service';
import { WeatherLoggerService } from '../services/weather-logger.service';

interface StudentLoan {
  id: number;
  bookTitle: string;
  author: string;
  dueDate: Date;
  daysLeft: number;
  isOverdue: boolean;
  renewalCount: number;
  maxRenewals: number;
}

interface CampusEvent {
  id: number;
  title: string;
  date: Date;
  location: string;
  type: 'academic' | 'library' | 'social' | 'workshop';
  description: string;
}

interface StudySchedule {
  id: number;
  subject: string;
  time: string;
  room: string;
  instructor: string;
  type: 'lecture' | 'lab' | 'exam' | 'assignment';
}

@Component({
  selector: 'app-student-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './student-dashboard.component.html',
  styles: []
})
export class StudentDashboardComponent extends BaseDashboardComponent implements OnInit, OnDestroy {
  private weatherSubscription?: Subscription;
  private clockSubscription?: Subscription;

  // Make Math available in template
  Math = Math;

  // Student-specific data
  currentStudent: any = null;
  currentLoans: StudentLoan[] = [];
  upcomingEvents: CampusEvent[] = [];
  todaySchedule: StudySchedule[] = [];
  
  // Dashboard stats
  totalBooksLoaned: number = 0;
  overdueBooks: number = 0;
  availableRenewals: number = 0;
  libraryFines: number = 0;

  // Quick search
  searchQuery: string = '';
  searchResults: any[] = [];
  isSearching: boolean = false;

  // Current time
  currentTime: string = '';
  currentDate: string = '';

  constructor(
    protected override router: Router,
    protected override authService: AuthService,
    private weatherService: WeatherLoggerService
  ) {
    super(router, authService);
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.loadStudentData();
    this.startClock();
  }

  override ngOnDestroy(): void {
    this.weatherSubscription?.unsubscribe();
    this.clockSubscription?.unsubscribe();
  }

  protected initializeDashboard(): void {
    const savedMode = localStorage.getItem('darkMode');
    this.isDarkMode = savedMode === 'true';
    this.loadWeatherData();
    this.loadStudentLoans();
    this.loadCampusEvents();
    this.loadTodaySchedule();
  }

  protected getLogoutRedirectRoute(): string {
    return '/login';
  }



  private loadStudentData(): void {
    // Get current student from auth service
    // This would typically come from your student auth service
    this.currentStudent = {
      studentId: '2024-12345',
      fullName: 'Juan Dela Cruz',
      course: 'BS Computer Science',
      yearLevel: '3rd Year',
      section: 'A'
    };
  }

  private loadWeatherData(): void {
    // Mock weather data for now - replace with actual service call
    this.temperature = '29°C';
    this.location = 'Cebu City';
    this.weatherIcon = 'rainy';

    // Uncomment when weather service is properly implemented
    /*
    this.weatherSubscription = this.weatherService.getWeatherData().subscribe({
      next: (data: any) => {
        if (data.success) {
          this.temperature = `${Math.round(data.data.temperature)}°C`;
          this.location = data.data.location;
          this.weatherIcon = this.getWeatherIcon(data.data.condition);
        }
      },
      error: (error: any) => {
        console.error('Weather data error:', error);
      }
    });
    */
  }

  private getWeatherIcon(condition: string): string {
    const iconMap: { [key: string]: string } = {
      'clear': 'sunny',
      'clouds': 'cloudy',
      'rain': 'rainy',
      'thunderstorm': 'stormy',
      'snow': 'snowy',
      'mist': 'foggy',
      'fog': 'foggy'
    };
    return iconMap[condition.toLowerCase()] || 'sunny';
  }

  private startClock(): void {
    this.updateTime();
    this.clockSubscription = interval(1000).subscribe(() => {
      this.updateTime();
    });
  }

  private updateTime(): void {
    const now = new Date();
    this.currentTime = now.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    this.currentDate = now.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  private loadStudentLoans(): void {
    // Mock data - replace with actual API call
    this.currentLoans = [
      {
        id: 1,
        bookTitle: 'Introduction to Algorithms',
        author: 'Thomas H. Cormen',
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
        daysLeft: 5,
        isOverdue: false,
        renewalCount: 1,
        maxRenewals: 3
      },
      {
        id: 2,
        bookTitle: 'Clean Code',
        author: 'Robert C. Martin',
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        daysLeft: 2,
        isOverdue: false,
        renewalCount: 0,
        maxRenewals: 3
      },
      {
        id: 3,
        bookTitle: 'Database Systems',
        author: 'Ramez Elmasri',
        dueDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        daysLeft: -1,
        isOverdue: true,
        renewalCount: 3,
        maxRenewals: 3
      }
    ];

    this.updateLoanStats();
  }

  private updateLoanStats(): void {
    this.totalBooksLoaned = this.currentLoans.length;
    this.overdueBooks = this.currentLoans.filter(loan => loan.isOverdue).length;
    this.availableRenewals = this.currentLoans.reduce((sum, loan) => 
      sum + (loan.maxRenewals - loan.renewalCount), 0);
    this.libraryFines = this.overdueBooks * 5; // ₱5 per overdue book per day
  }

  private loadCampusEvents(): void {
    // Mock data - replace with actual API call
    this.upcomingEvents = [
      {
        id: 1,
        title: 'Library Research Workshop',
        date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        location: 'Library Conference Room',
        type: 'workshop',
        description: 'Learn advanced research techniques and database navigation'
      },
      {
        id: 2,
        title: 'Midterm Examinations',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        location: 'Various Rooms',
        type: 'academic',
        description: 'Midterm examination period begins'
      },
      {
        id: 3,
        title: 'Book Fair 2024',
        date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
        location: 'Campus Quadrangle',
        type: 'library',
        description: 'Annual book fair with discounted academic books'
      }
    ];
  }

  private loadTodaySchedule(): void {
    // Mock data - replace with actual API call
    this.todaySchedule = [
      {
        id: 1,
        subject: 'Data Structures',
        time: '8:00 AM - 9:30 AM',
        room: 'CS-201',
        instructor: 'Prof. Maria Santos',
        type: 'lecture'
      },
      {
        id: 2,
        subject: 'Database Systems Lab',
        time: '10:00 AM - 12:00 PM',
        room: 'CS-Lab-1',
        instructor: 'Prof. Juan Reyes',
        type: 'lab'
      },
      {
        id: 3,
        subject: 'Software Engineering',
        time: '2:00 PM - 3:30 PM',
        room: 'CS-301',
        instructor: 'Prof. Ana Garcia',
        type: 'lecture'
      }
    ];
  }

  protected getAIResponses(userMessage: string): string[] {
    const message = userMessage.toLowerCase();
    
    if (message.includes('book') || message.includes('search')) {
      return [
        "I can help you search for books! Try using keywords like the title, author, or subject. What are you looking for?",
        "You can search our library catalog by title, author, ISBN, or subject. What book do you need?",
        "I'd be happy to help you find books! What subject or specific title are you interested in?"
      ];
    }
    
    if (message.includes('due') || message.includes('return')) {
      return [
        "You can check your due dates in the 'My Loans' section. Would you like me to show you your current loans?",
        "Your borrowed books and due dates are displayed in your dashboard. Need help with renewals?",
        "I can help you track your book due dates. Check the 'Current Loans' widget for details!"
      ];
    }
    
    if (message.includes('renew')) {
      return [
        "You can renew books if you haven't reached the maximum renewal limit. Check your loans section!",
        "Book renewals are available if no one else has reserved the book. Would you like to renew a specific book?",
        "I can help you with book renewals! Which book would you like to renew?"
      ];
    }
    
    if (message.includes('fine') || message.includes('fee')) {
      return [
        "You can check your library fines in the account summary. Current fines are displayed in your dashboard.",
        "Library fines are ₱5 per day for overdue books. You can pay at the library counter.",
        "Your current fines are shown in the dashboard. Visit the library to settle any outstanding fees."
      ];
    }
    
    return [
      "I'm here to help with your library needs! You can ask about books, due dates, renewals, or library services.",
      "How can I assist you today? I can help with book searches, account information, or library resources.",
      "I'm BC-AI, your library assistant! Feel free to ask about books, your account, or library services."
    ];
  }

  // Student-specific methods
  onNavigate(section: string): void {
    console.log(`Student navigating to ${section}`);
    // Implement student-specific navigation
  }

  onNotificationClick(): void {
    console.log('Student notifications clicked');
    // Show student notifications (due dates, announcements, etc.)
  }

  onProfileClick(): void {
    console.log('Student profile clicked');
    // Show student profile management
  }

  // Book search methods
  onQuickSearch(): void {
    if (!this.searchQuery.trim()) return;
    
    this.isSearching = true;
    // Simulate search delay
    setTimeout(() => {
      this.performSearch();
      this.isSearching = false;
    }, 1000);
  }

  private performSearch(): void {
    // Mock search results - replace with actual API call
    this.searchResults = [
      {
        title: 'Introduction to Algorithms',
        author: 'Thomas H. Cormen',
        availability: 'Available',
        location: 'CS Section - Shelf A3'
      },
      {
        title: 'Clean Code',
        author: 'Robert C. Martin',
        availability: 'Checked Out',
        location: 'CS Section - Shelf B1'
      }
    ];
  }

  // Loan management methods
  renewBook(loanId: number): void {
    const loan = this.currentLoans.find(l => l.id === loanId);
    if (loan && loan.renewalCount < loan.maxRenewals) {
      loan.renewalCount++;
      loan.dueDate = new Date(loan.dueDate.getTime() + 14 * 24 * 60 * 60 * 1000); // Add 14 days
      loan.daysLeft = Math.ceil((loan.dueDate.getTime() - Date.now()) / (24 * 60 * 60 * 1000));
      loan.isOverdue = loan.daysLeft < 0;
      this.updateLoanStats();
      console.log(`Book renewed: ${loan.bookTitle}`);
    }
  }

  getDueDateClass(loan: StudentLoan): string {
    if (loan.isOverdue) return 'text-red-600';
    if (loan.daysLeft <= 3) return 'text-yellow-600';
    return 'text-green-600';
  }

  getEventTypeIcon(type: string): string {
    const icons = {
      'academic': 'M12 14l9-5-9-5-9 5 9 5z',
      'library': 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
      'social': 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
      'workshop': 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z'
    };
    return icons[type as keyof typeof icons] || icons.academic;
  }
}
