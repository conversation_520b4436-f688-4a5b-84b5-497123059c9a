<!-- Modern Interactive Student Dashboard -->
<div class="min-h-screen transition-colors duration-300 flex" [class]="isDarkMode ? 'bg-gray-900' : 'bg-gray-50'" [attr.data-theme]="isDarkMode ? 'dark' : 'light'">
  <!-- Mobile Menu Button -->
  <button
    (click)="toggleMobileMenu()"
    class="lg:hidden fixed top-4 left-4 z-50 p-2 rounded-lg transition-colors duration-200"
    [class]="isDarkMode ? 'bg-gray-800 text-white hover:bg-gray-700' : 'bg-white text-gray-800 hover:bg-gray-100'"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
    </svg>
  </button>

  <!-- Left Navigation Pane (Fixed) -->
  <aside
    class="w-64 shadow-lg flex-shrink-0 border-r flex flex-col transition-all duration-300"
    [class]="getAsideClasses()"
    [style.transform]="isMobileMenuOpen ? 'translateX(0)' : ''"
  >
    <!-- Header -->
    <div class="p-6 border-b transition-colors duration-300" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <div class="flex items-center justify-center">
        <img
          src="assets/images/BcLogo.png"
          alt="Benedicto College Logo"
          class="h-12 w-auto object-contain"
        >
      </div>
    </div>

    <!-- Navigation Links -->
    <nav class="flex-1 p-4">
      <div class="space-y-2">
        <a (click)="onNavigate('dashboard'); closeMobileMenu()" class="nav-link active flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
          </svg>
          Dashboard
        </a>
        <a (click)="onNavigate('search'); closeMobileMenu()" class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          Search Books
        </a>
        <a (click)="onNavigate('loans'); closeMobileMenu()" class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
          My Loans
        </a>
        <a (click)="onNavigate('reservations'); closeMobileMenu()" class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h6m-6 0l-.5 8.5A2 2 0 0013.5 21h-3A2 2 0 018.5 15.5L8 7z"></path>
          </svg>
          Reservations
        </a>
        <a (click)="onNavigate('history'); closeMobileMenu()" class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer">
          <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          History
        </a>
      </div>
    </nav>

    <!-- Logout Button -->
    <div class="p-4 border-t transition-colors duration-300" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
      <button (click)="showLogout()" class="logout-btn w-full flex items-center justify-center px-4 py-3 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3v1"></path>
        </svg>
        Logout
      </button>
    </div>
  </aside>

  <!-- Main Content Area -->
  <div class="flex-1 flex">
    <!-- Dashboard Content -->
    <div class="flex-1 flex flex-col">
      <!-- Top Header -->
      <header [class]="getHeaderClasses()">
        <div class="flex justify-between items-center min-h-[64px]">
          <div class="flex-1 min-w-0">
            <h2 class="text-xl lg:text-2xl font-bold truncate" [class]="getTextClasses()">Student Dashboard</h2>
          </div>
          <div class="flex items-center space-x-2 lg:space-x-4 flex-shrink-0">
            <!-- Dark Mode Toggle -->
            <button
              (click)="toggleDarkMode()"
              class="p-2 rounded-lg transition-colors duration-200"
              [class]="isDarkMode ? 'text-yellow-400 hover:text-yellow-300' : 'text-gray-600 hover:text-gray-900'"
              title="Toggle Dark Mode"
            >
              <svg *ngIf="!isDarkMode" class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 718.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
              </svg>
              <svg *ngIf="isDarkMode" class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </button>
            <!-- Notification Bell -->
            <button
              (click)="onNotificationClick()"
              class="relative p-2 transition-colors duration-200"
              [class]="isDarkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'"
            >
              <svg class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
              </svg>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white notification-badge"></span>
            </button>
            <!-- User Profile -->
            <div class="relative">
              <button
                (click)="onProfileClick()"
                class="flex items-center space-x-2 p-2 rounded-lg transition-colors duration-200"
                [class]="isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'"
              >
                <img class="w-7 h-7 lg:w-8 lg:h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Crect width='32' height='32' fill='%233B82F6'/%3E%3Ctext x='16' y='20' text-anchor='middle' fill='white' font-family='Arial' font-size='14' font-weight='bold'%3ES%3C/text%3E%3C/svg%3E" alt="Student">
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Dashboard Content -->
      <main class="flex-1 p-6 overflow-y-auto transition-colors duration-300" [class]="getMainContentClasses()">
        <div class="max-w-7xl mx-auto">
          <!-- Latest News Section -->
          <div class="mb-8">
            <h3 class="text-xl font-semibold mb-4" [class]="getTextClasses()">Welcome Back, {{ currentStudent?.fullName || 'Student' }}!</h3>
            <div class="space-y-3">
              <div class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  <p [class]="getSecondaryTextClasses()">You have {{ totalBooksLoaned }} books currently borrowed.</p>
                </div>
              </div>
              <div class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                  <p [class]="getSecondaryTextClasses()">{{ availableRenewals }} renewals available.</p>
                </div>
              </div>
              <div class="p-4 rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200" [class]="getCardClasses()">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                  <p [class]="getSecondaryTextClasses()">{{ overdueBooks }} overdue books.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Search Section -->
          <div class="mb-8">
            <h3 class="text-xl font-semibold mb-4" [class]="getTextClasses()">Quick Book Search</h3>
            <div class="p-6 rounded-lg shadow-sm border" [class]="getCardClasses()">
              <div class="flex space-x-4">
                <input
                  [(ngModel)]="searchQuery"
                  (keyup.enter)="onQuickSearch()"
                  type="text"
                  placeholder="Search for books, authors, or subjects..."
                  class="flex-1 px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                  [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'"
                >
                <button
                  (click)="onQuickSearch()"
                  [disabled]="isSearching"
                  class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200"
                >
                  <span *ngIf="!isSearching">Search</span>
                  <span *ngIf="isSearching">Searching...</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Student Stats Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="p-6 rounded-lg shadow-sm border hover:shadow-md transition-all duration-200" [class]="getCardClasses()">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 mr-4">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-2xl font-bold" [class]="getTextClasses()">{{ totalBooksLoaned }}</p>
                  <p class="text-sm" [class]="getSecondaryTextClasses()">Books Borrowed</p>
                </div>
              </div>
            </div>

            <div class="p-6 rounded-lg shadow-sm border hover:shadow-md transition-all duration-200" [class]="getCardClasses()">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 mr-4">
                  <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-2xl font-bold" [class]="getTextClasses()">{{ overdueBooks }}</p>
                  <p class="text-sm" [class]="getSecondaryTextClasses()">Overdue Books</p>
                </div>
              </div>
            </div>

            <div class="p-6 rounded-lg shadow-sm border hover:shadow-md transition-all duration-200" [class]="getCardClasses()">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 mr-4">
                  <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-2xl font-bold" [class]="getTextClasses()">{{ availableRenewals }}</p>
                  <p class="text-sm" [class]="getSecondaryTextClasses()">Renewals Available</p>
                </div>
              </div>
            </div>

            <div class="p-6 rounded-lg shadow-sm border hover:shadow-md transition-all duration-200" [class]="getCardClasses()">
              <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 mr-4">
                  <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-2xl font-bold" [class]="getTextClasses()">₱{{ libraryFines }}</p>
                  <p class="text-sm" [class]="getSecondaryTextClasses()">Outstanding Fines</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Right Sidebar -->
    <aside class="w-80 shadow-lg border-l flex-shrink-0 transition-colors duration-300" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
      <div class="p-6 space-y-6">
        <!-- Weather Widget -->
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-lg font-semibold mb-1">Weather</h4>
              <p class="text-3xl font-bold" id="temperature">{{ temperature }}</p>
              <p class="text-blue-100" id="location">{{ location }}</p>
            </div>
            <div class="text-right">
              <svg class="w-12 h-12 text-yellow-300" fill="currentColor" viewBox="0 0 24 24" id="weather-icon">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
          </div>
        </div>

        <!-- Horoscope Widget -->
        <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
          <div class="flex items-center mb-3">
            <svg class="w-6 h-6 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <h4 class="text-lg font-semibold" [class]="getTextClasses()">Horoscope</h4>
          </div>
          <p [class]="getSecondaryTextClasses()"><strong>Cancer:</strong> Study focus brings good results today.</p>
        </div>

        <!-- Calendar Events Widget -->
        <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
          <div class="flex items-center mb-4">
            <svg class="w-6 h-6 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <h4 class="text-lg font-semibold" [class]="getTextClasses()">Upcoming Events</h4>
          </div>
          <div class="space-y-3">
            <div class="flex items-center" *ngFor="let event of upcomingEvents">
              <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <div>
                <p class="font-medium" [class]="getTextClasses()">{{ event.title }}</p>
                <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">{{ event.date }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Quote of the Day Widget -->
        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-6 text-white">
          <div class="flex items-center mb-3">
            <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
            </svg>
            <h4 class="text-lg font-semibold">Quote of the Day</h4>
          </div>
          <p class="text-purple-100 italic">"Education is the most powerful weapon which you can use to change the world."</p>
          <p class="text-purple-200 text-sm mt-2">- Nelson Mandela</p>
        </div>

        <!-- Study Schedule Widget -->
        <div class="border rounded-lg p-6 transition-colors duration-300" [class]="getCardClasses()">
          <div class="flex items-center mb-4">
            <svg class="w-6 h-6 text-orange-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h4 class="text-lg font-semibold" [class]="getTextClasses()">Today's Schedule</h4>
          </div>
          <div class="space-y-3">
            <div class="flex items-center" *ngFor="let schedule of todaySchedule">
              <div class="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
              <div>
                <p class="font-medium" [class]="getTextClasses()">{{ schedule.subject }}</p>
                <p class="text-sm" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">{{ schedule.time }} - {{ schedule.room }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </aside>
  </div>

  <!-- Logout Confirmation Modal -->
  <div *ngIf="showLogoutModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-4" [class]="isDarkMode ? 'bg-gray-800' : 'bg-white'">
      <h3 class="text-lg font-semibold mb-4" [class]="getTextClasses()">Confirm Logout</h3>
      <p class="mb-6" [class]="getSecondaryTextClasses()">Are you sure you want to log out?</p>
      <div class="flex space-x-4">
        <button
          (click)="confirmLogout()"
          class="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
        >
          Yes
        </button>
        <button
          (click)="cancelLogout()"
          class="flex-1 px-4 py-2 border rounded-lg transition-colors duration-200"
          [class]="isDarkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'"
        >
          No
        </button>
      </div>
    </div>
  </div>

  <!-- Chat Widget -->
  <div class="fixed bottom-6 right-6 z-40">
    <!-- Chat Button -->
    <button
      *ngIf="!isChatOpen"
      (click)="toggleChat()"
      (mouseenter)="showTooltip = true"
      (mouseleave)="showTooltip = false"
      class="relative bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg transition-all duration-300 transform hover:scale-110"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
      </svg>

      <!-- Notification Badge -->
      <span *ngIf="hasUnreadMessages" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
        {{ unreadCount }}
      </span>

      <!-- Tooltip -->
      <div *ngIf="showTooltip" class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg whitespace-nowrap">
        Need help finding books?
        <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
      </div>
    </button>

    <!-- Chat Window -->
    <div *ngIf="isChatOpen" class="bg-white rounded-lg shadow-xl w-80 h-96 flex flex-col" [class]="isDarkMode ? 'bg-gray-800' : 'bg-white'">
      <!-- Chat Header -->
      <div class="flex items-center justify-between p-4 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
        <div class="flex items-center space-x-2">
          <img
            src="assets/images/bc-ai-avatar.svg"
            alt="BC-AI"
            class="w-8 h-8 rounded-full bg-blue-500"
            (error)="onAvatarError($event)"
          >
          <div>
            <h4 class="font-semibold" [class]="getTextClasses()">BC-AI</h4>
            <p class="text-xs" [class]="getSecondaryTextClasses()">Library Assistant</p>
          </div>
        </div>
        <button (click)="toggleChat()" class="text-gray-500 hover:text-gray-700">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Chat Messages Area -->
      <div class="flex-1 p-4 overflow-y-auto space-y-3" #chatMessagesContainer>
        <div *ngFor="let message of chatMessages" class="flex" [class.justify-end]="message.isUser">
          <div *ngIf="!message.isUser" class="flex items-start space-x-2 max-w-xs">
            <img
              src="assets/images/bc-ai-avatar.svg"
              alt="BC-AI"
              class="w-6 h-6 rounded-full bg-blue-500 flex-shrink-0 mt-1"
              (error)="onAvatarError($event)"
            >
            <div class="bg-gray-100 rounded-lg px-3 py-2" [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-100'">
              <p class="text-sm" [class]="getTextClasses()">{{ message.text }}</p>
            </div>
          </div>
          <div *ngIf="message.isUser" class="bg-blue-600 text-white rounded-lg px-3 py-2 max-w-xs">
            <p class="text-sm">{{ message.text }}</p>
          </div>
        </div>

        <!-- Typing Indicator -->
        <div *ngIf="isTyping" class="flex items-start space-x-2">
          <img
            src="assets/images/bc-ai-avatar.svg"
            alt="BC-AI"
            class="w-6 h-6 rounded-full bg-blue-500 flex-shrink-0 mt-1"
            (error)="onAvatarError($event)"
          >
          <div class="bg-gray-100 rounded-lg px-3 py-2" [class]="isDarkMode ? 'bg-gray-700' : 'bg-gray-100'">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Input -->
      <div class="p-4 border-t" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
        <div class="flex space-x-2">
          <input
            [(ngModel)]="chatInput"
            (keyup.enter)="sendMessage()"
            type="text"
            placeholder="Ask about books..."
            class="flex-1 px-3 py-2 border rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'"
          >
          <button
            (click)="sendMessage()"
            [disabled]="!chatInput.trim()"
            class="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
