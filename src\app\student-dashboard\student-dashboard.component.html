<!-- Student Dashboard -->
<div class="min-h-screen transition-colors duration-300" [class]="isDarkMode ? 'bg-gray-900' : 'bg-gray-50'">
  <div class="flex">
    <!-- Left Navigation -->
    <aside class="w-64 shadow-lg flex-shrink-0 border-r flex flex-col transition-all duration-300" [class]="getAsideClasses()">
      <!-- Header -->
      <div class="p-6 border-b" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
        <div class="flex items-center justify-center">
          <img src="assets/images/BcLogo.png" alt="Benedicto College Logo" class="h-12 w-auto object-contain">
        </div>
      </div>

      <!-- Navigation Links -->
      <nav class="flex-1 p-4">
        <div class="space-y-2">
          <a (click)="onNavigate('dashboard')" class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer" [class]="getNavLinkClasses()">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            </svg>
            Dashboard
          </a>
          <a (click)="onNavigate('search')" class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer" [class]="getNavLinkClasses()">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            Search Books
          </a>
          <a (click)="onNavigate('loans')" class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer" [class]="getNavLinkClasses()">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
            My Loans
          </a>
          <a (click)="onNavigate('reservations')" class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer" [class]="getNavLinkClasses()">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h6m-6 0l-.5 8.5A2 2 0 0013.5 21h-3A2 2 0 018.5 15.5L8 7z"></path>
            </svg>
            Reservations
          </a>
          <a (click)="onNavigate('history')" class="nav-link flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 cursor-pointer" [class]="getNavLinkClasses()">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            History
          </a>
        </div>
      </nav>

      <!-- Bottom Actions -->
      <div class="p-4 border-t" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
        <div class="space-y-2">
          <button (click)="toggleDarkMode()" class="w-full flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200" [class]="getNavLinkClasses()">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
            </svg>
            {{ isDarkMode ? 'Light Mode' : 'Dark Mode' }}
          </button>
          <button (click)="showLogout()" class="w-full flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 text-red-600 hover:bg-red-50 hover:text-red-700">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            Logout
          </button>
        </div>
      </div>
    </aside>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col">
      <!-- Top Header -->
      <header class="shadow-sm border-b px-6 py-4" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
        <div class="flex items-center justify-between">
          <h1 class="text-xl font-semibold" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Student Dashboard</h1>
          <div class="flex items-center space-x-4">
            <button (click)="toggleDarkMode()" class="p-2 transition-colors duration-200" [class]="isDarkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 718.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
              </svg>
            </button>
            <button (click)="onNotificationClick()" class="relative p-2 transition-colors duration-200" [class]="isDarkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
              </svg>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
            </button>
            <button (click)="onProfileClick()" class="p-2 rounded-lg transition-colors duration-200" [class]="isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'">
              <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-bold">S</div>
            </button>
          </div>
        </div>
      </header>

      <!-- Main Dashboard Content -->
      <main class="flex-1 p-6 overflow-y-auto" [class]="isDarkMode ? 'bg-gray-900' : 'bg-gray-50'">
        <div class="max-w-7xl mx-auto">
          <!-- Welcome Section -->
          <div class="mb-8">
            <h3 class="text-xl font-semibold mb-4" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Welcome Back, {{ currentStudent?.fullName || 'Student' }}!</h3>
            <div class="space-y-3">
              <div class="p-4 rounded-lg shadow-sm border" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  <p [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">You have {{ totalBooksLoaned }} books currently borrowed.</p>
                </div>
              </div>
              <div class="p-4 rounded-lg shadow-sm border" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                  <p [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">{{ availableRenewals }} renewals available for your books.</p>
                </div>
              </div>
              <div *ngIf="overdueBooks > 0" class="p-4 rounded-lg shadow-sm border" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
                <div class="flex items-center">
                  <div class="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                  <p [class]="isDarkMode ? 'text-gray-300' : 'text-gray-700'">{{ overdueBooks }} overdue books - please return soon!</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Current Loans Section -->
          <div class="mb-8">
            <h3 class="text-xl font-semibold mb-4" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">My Current Loans</h3>
            <div class="p-6 rounded-lg shadow-sm border" [class]="isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'">
              <div *ngIf="currentLoans.length === 0" class="text-center py-8">
                <svg class="mx-auto h-12 w-12 mb-4" [class]="isDarkMode ? 'text-gray-600' : 'text-gray-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                <p class="text-lg font-medium" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">No books currently borrowed</p>
                <p class="text-sm mt-1" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">Visit the library to borrow books for your studies</p>
              </div>
              
              <div *ngIf="currentLoans.length > 0" class="space-y-4">
                <div *ngFor="let loan of currentLoans" class="flex items-center">
                  <div class="flex-shrink-0 mr-4">
                    <svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <p class="text-lg font-medium" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">{{ loan.bookTitle }}</p>
                    <p class="text-sm mt-1" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-500'">by {{ loan.author }} • Due: {{ loan.dueDate | date:'MMM d, y' }}</p>
                  </div>
                  <div class="ml-4">
                    <span class="px-3 py-1 text-xs font-medium rounded-full" [class]="getDueDateClass(loan)">
                      <span *ngIf="loan.isOverdue">{{ Math.abs(loan.daysLeft) }} days overdue</span>
                      <span *ngIf="!loan.isOverdue && loan.daysLeft <= 3">{{ loan.daysLeft }} days left</span>
                      <span *ngIf="!loan.isOverdue && loan.daysLeft > 3">{{ loan.daysLeft }} days left</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- Logout Modal -->
  <div *ngIf="showLogoutModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="rounded-lg p-6 max-w-sm w-full mx-4" [class]="isDarkMode ? 'bg-gray-800' : 'bg-white'">
      <h3 class="text-lg font-medium mb-4" [class]="isDarkMode ? 'text-white' : 'text-gray-900'">Confirm Logout</h3>
      <p class="text-sm mb-6" [class]="isDarkMode ? 'text-gray-400' : 'text-gray-600'">Are you sure you want to log out?</p>
      <div class="flex space-x-3">
        <button (click)="cancelLogout()" class="flex-1 px-4 py-2 text-sm font-medium rounded-md" [class]="isDarkMode ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-200 text-gray-900 hover:bg-gray-300'">Cancel</button>
        <button (click)="confirmLogout()" class="flex-1 px-4 py-2 text-sm font-medium rounded-md bg-red-600 text-white hover:bg-red-700">Logout</button>
      </div>
    </div>
  </div>

  <!-- BC-AI Chat Widget -->
  <div class="fixed bottom-6 right-6 z-40">
    <div class="relative">
      <button (click)="toggleChat()" class="w-14 h-14 bg-blue-600 hover:bg-blue-700 rounded-full shadow-lg flex items-center justify-center transition-all duration-300 transform hover:scale-110">
        <svg *ngIf="!isChatOpen" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
        <svg *ngIf="isChatOpen" class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Chat Window -->
    <div *ngIf="isChatOpen" class="absolute bottom-16 right-0 w-80 h-96 rounded-lg shadow-2xl flex flex-col" [class]="isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'">
      <!-- Chat Header -->
      <div class="flex items-center justify-between p-4 border-b rounded-t-lg bg-blue-600">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
            <span class="text-white text-sm font-bold">AI</span>
          </div>
          <div>
            <h4 class="font-medium text-white">BC-AI</h4>
            <p class="text-xs text-blue-100">Library Assistant</p>
          </div>
        </div>
        <button (click)="toggleChat()" class="text-white hover:text-gray-200">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Chat Messages -->
      <div #chatMessagesContainer class="flex-1 overflow-y-auto p-4 space-y-3">
        <div *ngFor="let message of chatMessages" class="flex" [class.justify-end]="message.isUser">
          <div class="max-w-xs px-3 py-2 rounded-lg text-sm" [class]="message.isUser ? 'bg-blue-600 text-white' : (isDarkMode ? 'bg-gray-700 text-gray-200' : 'bg-gray-100 text-gray-900')">
            {{ message.text }}
          </div>
        </div>
        
        <div *ngIf="isTyping" class="flex">
          <div class="max-w-xs px-3 py-2 rounded-lg text-sm" [class]="isDarkMode ? 'bg-gray-700 text-gray-200' : 'bg-gray-100 text-gray-900'">
            <div class="flex space-x-1">
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Input -->
      <div class="p-4 border-t" [class]="isDarkMode ? 'border-gray-700' : 'border-gray-200'">
        <div class="flex space-x-2">
          <input type="text" [(ngModel)]="chatInput" (keypress)="onChatKeyPress($event)" placeholder="Ask about books..." class="flex-1 px-3 py-2 border rounded-lg text-sm" [class]="isDarkMode ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'">
          <button (click)="sendMessage()" [disabled]="!chatInput.trim() || isTyping" class="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
